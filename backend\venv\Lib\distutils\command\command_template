"""distutils.command.x

Implements the Distutils 'x' command.
"""

# created 2000/mm/dd, <PERSON>

__revision__ = "$Id$"

from distutils.core import Command


class x(Command):

    # Brief (40-50 characters) description of the command
    description = ""

    # List of option tuples: long name, short name (None if no short
    # name), and help string.
    user_options = [('', '',
                     ""),
                   ]

    def initialize_options(self):
        self. = None
        self. = None
        self. = None

    def finalize_options(self):
        if self.x is None:
            self.x = 

    def run(self):
